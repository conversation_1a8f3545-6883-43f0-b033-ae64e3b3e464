#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十年期美债和中债收益率数据收集器
使用akshare获取十年期美国国债和中国国债收益率数据
"""

import akshare as ak
import pandas as pd
import yfinance as yf
import time
from datetime import datetime, timedelta
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BondYieldCollector:
    def __init__(self):
        """初始化债券收益率数据收集器"""
        self.data_dir = "data_files"
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 债券代码映射
        self.bond_symbols = {
            "美债10年": {
                "akshare_symbol": "TNX",  # 10年期美债收益率
                "yfinance_symbol": "^TNX",  # Yahoo Finance 10年期美债
                "name": "美国10年期国债收益率",
                "currency": "USD"
            },
            "中债10年": {
                "akshare_symbol": "中债国债到期收益率:10年",
                "name": "中国10年期国债收益率", 
                "currency": "CNY"
            }
        }
    
    def get_us_10y_yield_akshare(self, start_date=None, end_date=None):
        """
        使用akshare获取美国10年期国债收益率

        Args:
            start_date: 开始日期，格式 'YYYY-MM-DD'
            end_date: 结束日期，格式 'YYYY-MM-DD'

        Returns:
            pandas.DataFrame: 美债收益率数据
        """
        try:
            logger.info("正在使用akshare获取美国10年期国债收益率...")

            # 使用akshare获取美债收益率数据（不传递日期参数）
            us_yield_data = ak.bond_zh_us_rate()

            if us_yield_data is not None and not us_yield_data.empty:
                logger.info(f"获取到美债数据，列名: {list(us_yield_data.columns)}")

                # 查找包含10年的列
                ten_year_columns = [col for col in us_yield_data.columns if '10年' in col or '10Y' in col or 'TNX' in col]

                if ten_year_columns:
                    result_data = us_yield_data[['日期', ten_year_columns[0]]].copy()
                    result_data.columns = ['日期', '收益率']

                    # 数据清理
                    result_data['日期'] = pd.to_datetime(result_data['日期'])
                    result_data['收益率'] = pd.to_numeric(result_data['收益率'], errors='coerce')
                    result_data = result_data.dropna()
                    result_data = result_data.sort_values('日期').reset_index(drop=True)

                    # 添加标识列
                    result_data['债券类型'] = '美债10年'
                    result_data['货币'] = 'USD'

                    logger.info(f"成功获取 {len(result_data)} 条美债10年期收益率数据")
                    return result_data
                else:
                    logger.info("未找到10年期美债列，尝试其他方法...")
                    return self.get_us_10y_yield_alternative()
            else:
                logger.warning("akshare未获取到美债数据，尝试备用方法...")
                return self.get_us_10y_yield_alternative()

        except Exception as e:
            logger.error(f"使用akshare获取美债数据时出错: {str(e)}")
            logger.info("尝试备用方法...")
            return self.get_us_10y_yield_alternative()
    
    def get_us_10y_yield_alternative(self):
        """
        使用akshare的其他接口获取美债收益率
        """
        try:
            logger.info("使用akshare备用接口获取美债收益率...")

            # 尝试使用宏观经济数据接口
            us_yield_data = ak.macro_usa_treasury_rate()

            if us_yield_data is not None and not us_yield_data.empty:
                logger.info(f"备用方法获取到数据，列名: {list(us_yield_data.columns)}")

                # 查找10年期相关列
                ten_year_columns = [col for col in us_yield_data.columns if '10' in col]

                if ten_year_columns:
                    result_data = us_yield_data[['日期', ten_year_columns[0]]].copy()
                    result_data.columns = ['日期', '收益率']

                    # 数据清理
                    result_data['日期'] = pd.to_datetime(result_data['日期'])
                    result_data['收益率'] = pd.to_numeric(result_data['收益率'], errors='coerce')
                    result_data = result_data.dropna()
                    result_data = result_data.sort_values('日期').reset_index(drop=True)

                    # 添加标识列
                    result_data['债券类型'] = '美债10年'
                    result_data['货币'] = 'USD'

                    logger.info(f"备用方法成功获取 {len(result_data)} 条美债数据")
                    return result_data
                else:
                    logger.warning("备用方法未找到10年期数据，尝试yfinance...")
                    return self.get_us_10y_yield_yfinance()
            else:
                logger.warning("备用方法也未获取到数据，尝试yfinance...")
                return self.get_us_10y_yield_yfinance()

        except Exception as e:
            logger.error(f"备用方法获取美债数据时出错: {str(e)}")
            return self.get_us_10y_yield_yfinance()
    
    def get_us_10y_yield_yfinance(self, period="10y"):
        """
        使用yfinance获取美国10年期国债收益率

        Args:
            period: 数据周期，默认10年

        Returns:
            pandas.DataFrame: 美债收益率数据
        """
        try:
            logger.info("正在使用yfinance获取美国10年期国债收益率...")

            # 使用yfinance获取TNX数据（10年期美债收益率指数）
            ticker = yf.Ticker("^TNX")
            hist_data = ticker.history(period=period)

            if not hist_data.empty:
                logger.info(f"yfinance获取到数据，行数: {len(hist_data)}")

                # 创建结果DataFrame
                result_data = pd.DataFrame()
                result_data['日期'] = hist_data.index.date  # 只保留日期部分
                result_data['收益率'] = hist_data['Close']  # 使用收盘价作为收益率

                # 重置索引
                result_data = result_data.reset_index(drop=True)

                # 数据清理
                result_data['日期'] = pd.to_datetime(result_data['日期'])
                result_data['收益率'] = pd.to_numeric(result_data['收益率'], errors='coerce')
                result_data = result_data.dropna()
                result_data = result_data.sort_values('日期').reset_index(drop=True)

                # 添加标识列
                result_data['债券类型'] = '美债10年'
                result_data['货币'] = 'USD'

                logger.info(f"yfinance成功获取 {len(result_data)} 条美债10年期收益率数据")
                return result_data
            else:
                logger.error("yfinance未获取到美债数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"使用yfinance获取美债数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def get_china_10y_yield_akshare(self):
        """
        使用akshare获取中国10年期国债收益率

        Returns:
            pandas.DataFrame: 中债收益率数据
        """
        try:
            logger.info("正在使用akshare获取中国10年期国债收益率...")

            # 使用akshare获取中美债券收益率数据
            bond_data = ak.bond_zh_us_rate()

            if bond_data is not None and not bond_data.empty:
                logger.info(f"获取到债券数据，列名: {list(bond_data.columns)}")

                # 查找中国国债收益率10年列
                if '中国国债收益率10年' in bond_data.columns:
                    result_data = bond_data[['日期', '中国国债收益率10年']].copy()
                    result_data.columns = ['日期', '收益率']

                    # 数据清理
                    result_data['日期'] = pd.to_datetime(result_data['日期'])
                    result_data['收益率'] = pd.to_numeric(result_data['收益率'], errors='coerce')
                    result_data = result_data.dropna()
                    result_data = result_data.sort_values('日期').reset_index(drop=True)

                    # 添加标识列
                    result_data['债券类型'] = '中债10年'
                    result_data['货币'] = 'CNY'

                    logger.info(f"成功获取 {len(result_data)} 条中债10年期收益率数据")
                    return result_data
                else:
                    logger.warning("未找到中国国债收益率10年列，尝试其他方法...")
                    return self.get_china_10y_yield_alternative()
            else:
                logger.warning("未获取到债券数据，尝试备用方法...")
                return self.get_china_10y_yield_alternative()

        except Exception as e:
            logger.error(f"获取中债数据时出错: {str(e)}")
            return self.get_china_10y_yield_alternative()
    
    def get_china_10y_yield_alternative(self):
        """
        使用akshare的其他接口获取中债收益率
        """
        try:
            logger.info("使用akshare备用接口获取中债收益率...")

            # 尝试使用国债收益率曲线接口
            china_yield_data = ak.bond_china_yield()

            if china_yield_data is not None and not china_yield_data.empty:
                logger.info(f"备用方法获取到数据，列名: {list(china_yield_data.columns)}")

                # 查找10年期相关列
                ten_year_columns = [col for col in china_yield_data.columns if '10年' in col or '10Y' in col]

                if ten_year_columns:
                    result_data = china_yield_data[['日期', ten_year_columns[0]]].copy()
                    result_data.columns = ['日期', '收益率']

                    # 数据清理
                    result_data['日期'] = pd.to_datetime(result_data['日期'])
                    result_data['收益率'] = pd.to_numeric(result_data['收益率'], errors='coerce')
                    result_data = result_data.dropna()
                    result_data = result_data.sort_values('日期').reset_index(drop=True)

                    # 添加标识列
                    result_data['债券类型'] = '中债10年'
                    result_data['货币'] = 'CNY'

                    logger.info(f"备用方法成功获取 {len(result_data)} 条中债数据")
                    return result_data
                else:
                    logger.warning("备用方法未找到10年期中债数据")
                    return pd.DataFrame()
            else:
                logger.warning("备用方法未获取到中债数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"备用方法获取中债数据时出错: {str(e)}")
            # 尝试最后一种方法
            return self.get_china_10y_yield_last_resort()

    def get_china_10y_yield_last_resort(self):
        """
        最后一种获取中债收益率的方法
        """
        try:
            logger.info("使用最后一种方法获取中债收益率...")

            # 尝试使用宏观经济数据
            china_yield_data = ak.macro_china_treasury_rate()

            if china_yield_data is not None and not china_yield_data.empty:
                logger.info(f"最后方法获取到数据，列名: {list(china_yield_data.columns)}")

                # 查找10年期相关列
                ten_year_columns = [col for col in china_yield_data.columns if '10' in col]

                if ten_year_columns:
                    result_data = china_yield_data[['日期', ten_year_columns[0]]].copy()
                    result_data.columns = ['日期', '收益率']

                    # 数据清理
                    result_data['日期'] = pd.to_datetime(result_data['日期'])
                    result_data['收益率'] = pd.to_numeric(result_data['收益率'], errors='coerce')
                    result_data = result_data.dropna()
                    result_data = result_data.sort_values('日期').reset_index(drop=True)

                    # 添加标识列
                    result_data['债券类型'] = '中债10年'
                    result_data['货币'] = 'CNY'

                    logger.info(f"最后方法成功获取 {len(result_data)} 条中债数据")
                    return result_data
                else:
                    logger.warning("最后方法也未找到10年期中债数据")
                    return pd.DataFrame()
            else:
                logger.warning("最后方法未获取到中债数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"最后方法获取中债数据时出错: {str(e)}")
            return pd.DataFrame()

    def get_all_bond_yields(self, start_date=None, end_date=None):
        """
        获取所有债券收益率数据
        
        Args:
            start_date: 开始日期，格式 'YYYY-MM-DD'
            end_date: 结束日期，格式 'YYYY-MM-DD'
        
        Returns:
            dict: 包含美债和中债数据的字典
        """
        results = {}
        
        # 获取美债10年期收益率
        logger.info("开始获取美债10年期收益率...")
        us_data = self.get_us_10y_yield_akshare(start_date, end_date)
        if not us_data.empty:
            results['美债10年'] = us_data
            logger.info(f"✅ 美债10年: {len(us_data)} 条记录")
        else:
            logger.warning("❌ 美债10年: 无数据")
        
        # 添加延迟
        time.sleep(2)
        
        # 获取中债10年期收益率
        logger.info("开始获取中债10年期收益率...")
        china_data = self.get_china_10y_yield_akshare()
        if not china_data.empty:
            results['中债10年'] = china_data
            logger.info(f"✅ 中债10年: {len(china_data)} 条记录")
        else:
            logger.warning("❌ 中债10年: 无数据")
        
        return results
    
    def save_bond_data(self, data_dict, filename_prefix="bond_yields"):
        """
        保存债券收益率数据到CSV文件
        
        Args:
            data_dict: dict, 债券数据字典
            filename_prefix: str, 文件名前缀
        """
        if not data_dict:
            logger.warning("数据为空，无法保存")
            return
        
        # 保存单独的文件
        for bond_type, data in data_dict.items():
            if not data.empty:
                filename = f"{filename_prefix}_{bond_type}.csv"
                filepath = os.path.join(self.data_dir, filename)
                
                try:
                    data.to_csv(filepath, index=False, encoding='utf-8-sig')
                    logger.info(f"{bond_type}数据已保存到: {filepath}")
                    
                    # 显示数据概览
                    logger.info(f"{bond_type}数据概览:")
                    logger.info(f"  总记录数: {len(data)}")
                    logger.info(f"  日期范围: {data['日期'].min()} 到 {data['日期'].max()}")
                    logger.info(f"  收益率范围: {data['收益率'].min():.4f}% 到 {data['收益率'].max():.4f}%")
                    
                    # 显示最新数据
                    if len(data) > 0:
                        latest = data.iloc[-1]
                        logger.info(f"  最新收益率: {latest['收益率']:.4f}% ({latest['日期'].strftime('%Y-%m-%d')})")
                    
                except Exception as e:
                    logger.error(f"保存{bond_type}数据时出错: {str(e)}")
        
        # 保存合并文件
        if len(data_dict) > 1:
            try:
                all_data = []
                for data in data_dict.values():
                    if not data.empty:
                        all_data.append(data)
                
                if all_data:
                    combined_data = pd.concat(all_data, ignore_index=True)
                    combined_filepath = os.path.join(self.data_dir, f"{filename_prefix}_combined.csv")
                    combined_data.to_csv(combined_filepath, index=False, encoding='utf-8-sig')
                    logger.info(f"合并数据已保存到: {combined_filepath}")
                    
            except Exception as e:
                logger.error(f"保存合并数据时出错: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("十年期美债和中债收益率数据收集器")
    print("=" * 60)
    
    # 创建收集器实例
    collector = BondYieldCollector()
    
    # 获取债券收益率数据
    bond_data = collector.get_all_bond_yields()
    
    if bond_data:
        print(f"\n✅ 数据收集完成！")
        print(f"共获取 {len(bond_data)} 种债券的收益率数据")
        
        # 保存数据
        collector.save_bond_data(bond_data)
        
        # 显示结果摘要
        print("\n📊 债券收益率数据摘要:")
        print("-" * 50)
        for bond_type, data in bond_data.items():
            if not data.empty:
                latest = data.iloc[-1]
                print(f"{bond_type:>8}: {latest['收益率']:>8.4f}% ({latest['日期'].strftime('%Y-%m-%d')}) - {len(data)} 条记录")
    else:
        print("\n❌ 数据收集失败")

if __name__ == "__main__":
    main()
