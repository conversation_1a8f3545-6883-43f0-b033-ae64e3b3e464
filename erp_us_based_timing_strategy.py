#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于美债的ERP择时策略
使用新公式: ERP = 1/PE - 十年期美债收益率
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime
import logging

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    pass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ERPUSBasedTimingStrategy:
    def __init__(self):
        """初始化基于美债的ERP择时策略"""
        self.data_dir = "data_files"
        self.image_dir = "images"
        os.makedirs(self.image_dir, exist_ok=True)
        
        # 策略参数（基于新的ERP分布调整）
        self.strategies = {
            "保守策略": {
                "buy_percentile": 85,    # ERP第85分位以上买入
                "sell_percentile": 30,   # ERP第30分位以下卖出
                "min_hold_days": 30,
                "description": "只在ERP较高时买入"
            },
            "积极策略": {
                "buy_percentile": 70,    # ERP第70分位以上买入
                "sell_percentile": 20,   # ERP第20分位以下卖出
                "min_hold_days": 21,
                "description": "ERP中高时就买入"
            },
            "均衡策略": {
                "buy_percentile": 75,    # ERP第75分位以上买入
                "sell_percentile": 25,   # ERP第25分位以下卖出
                "min_hold_days": 30,
                "description": "平衡风险和收益"
            },
            "最近5年策略": {
                "years": 5,              # 只看最近5年数据
                "buy_percentile": 80,
                "sell_percentile": 20,
                "min_hold_days": 30,
                "description": "基于最近5年ERP分位数"
            }
        }
    
    def load_erp_data(self):
        """加载基于美债的ERP数据"""
        try:
            erp_file = os.path.join(self.data_dir, "hsi_erp_us_based_data.csv")
            if not os.path.exists(erp_file):
                logger.error(f"ERP数据文件不存在: {erp_file}")
                return None
            
            data = pd.read_csv(erp_file)
            data['日期'] = pd.to_datetime(data['日期'])
            data = data.sort_values('日期').reset_index(drop=True)
            
            logger.info(f"成功加载基于美债的ERP数据: {len(data)} 条记录")
            logger.info(f"日期范围: {data['日期'].min()} 到 {data['日期'].max()}")
            
            return data
            
        except Exception as e:
            logger.error(f"加载数据出错: {e}")
            return None
    
    def calculate_signals(self, data):
        """计算各种择时信号"""
        try:
            logger.info("计算择时信号...")
            
            result_data = data.copy()
            
            # 计算ERP历史分位数（全历史）
            result_data['ERP_分位数'] = result_data['ERP_百分比'].rank(pct=True) * 100
            
            # 计算最近5年分位数
            cutoff_date = datetime.now() - pd.DateOffset(years=5)
            recent_5y_data = result_data[result_data['日期'] >= cutoff_date]
            if len(recent_5y_data) > 0:
                # 为最近5年数据计算分位数
                recent_5y_percentiles = recent_5y_data['ERP_百分比'].rank(pct=True) * 100
                # 将分位数映射回原数据
                result_data.loc[result_data['日期'] >= cutoff_date, 'ERP_5年分位数'] = recent_5y_percentiles.values
            
            # 1. 保守策略信号
            strategy = self.strategies["保守策略"]
            result_data['保守_买入信号'] = result_data['ERP_分位数'] >= strategy["buy_percentile"]
            result_data['保守_卖出信号'] = result_data['ERP_分位数'] <= strategy["sell_percentile"]
            
            # 2. 积极策略信号
            strategy = self.strategies["积极策略"]
            result_data['积极_买入信号'] = result_data['ERP_分位数'] >= strategy["buy_percentile"]
            result_data['积极_卖出信号'] = result_data['ERP_分位数'] <= strategy["sell_percentile"]
            
            # 3. 均衡策略信号
            strategy = self.strategies["均衡策略"]
            result_data['均衡_买入信号'] = result_data['ERP_分位数'] >= strategy["buy_percentile"]
            result_data['均衡_卖出信号'] = result_data['ERP_分位数'] <= strategy["sell_percentile"]
            
            # 4. 最近5年策略信号
            strategy = self.strategies["最近5年策略"]
            result_data['最近5年_买入信号'] = result_data['ERP_5年分位数'] >= strategy["buy_percentile"]
            result_data['最近5年_卖出信号'] = result_data['ERP_5年分位数'] <= strategy["sell_percentile"]
            
            logger.info("择时信号计算完成")
            return result_data
            
        except Exception as e:
            logger.error(f"计算择时信号时出错: {e}")
            return None
    
    def backtest_strategy(self, data, strategy_name):
        """回测单个策略"""
        try:
            logger.info(f"回测策略: {strategy_name}")
            
            buy_signal_col = f"{strategy_name}_买入信号"
            sell_signal_col = f"{strategy_name}_卖出信号"
            
            # 找到对应的策略配置
            strategy_config = None
            for config_name, config in self.strategies.items():
                if strategy_name in config_name:
                    strategy_config = config
                    break
            
            if strategy_config is None:
                logger.error(f"未找到策略配置: {strategy_name}")
                return []
            
            min_hold_days = strategy_config["min_hold_days"]
            
            positions = []
            returns = []
            trades = []
            
            current_position = 0
            entry_price = 0
            entry_date = None
            
            for i, row in data.iterrows():
                date = row['日期']
                price = row['收盘价']
                buy_signal = row[buy_signal_col]
                sell_signal = row[sell_signal_col]
                erp = row['ERP_百分比']
                erp_percentile = row['ERP_分位数'] if 'ERP_分位数' in row else None
                erp_5y_percentile = row.get('ERP_5年分位数', None)
                
                # 交易逻辑
                if current_position == 0 and buy_signal:  # 买入
                    current_position = 1
                    entry_price = price
                    entry_date = date
                    trades.append({
                        '日期': date,
                        '操作': '买入',
                        '价格': price,
                        'ERP': erp,
                        'ERP_分位数': erp_percentile,
                        'ERP_5年分位数': erp_5y_percentile
                    })
                    
                elif current_position == 1 and sell_signal:  # 卖出
                    if entry_date is None or (date - entry_date).days >= min_hold_days:
                        current_position = 0
                        exit_return = (price - entry_price) / entry_price
                        hold_days = (date - entry_date).days if entry_date else 0
                        
                        trades.append({
                            '日期': date,
                            '操作': '卖出',
                            '价格': price,
                            'ERP': erp,
                            'ERP_分位数': erp_percentile,
                            'ERP_5年分位数': erp_5y_percentile,
                            '收益率': exit_return * 100,
                            '持有天数': hold_days
                        })
                
                positions.append(current_position)
                
                # 计算当日收益率
                if i == 0:
                    daily_return = 0
                else:
                    if current_position == 1:  # 持股
                        daily_return = (price - data.iloc[i-1]['收盘价']) / data.iloc[i-1]['收盘价']
                    else:  # 持现金
                        daily_return = 0
                
                returns.append(daily_return)
            
            # 添加策略结果到数据
            data[f'{strategy_name}_持仓'] = positions
            data[f'{strategy_name}_收益率'] = returns
            data[f'{strategy_name}_累计收益'] = (1 + pd.Series(returns)).cumprod()
            
            return trades
            
        except Exception as e:
            logger.error(f"回测策略时出错: {e}")
            return []
    
    def calculate_performance(self, data, strategy_name, trades):
        """计算策略绩效"""
        try:
            # 基本统计
            completed_trades = [t for t in trades if '收益率' in t]
            if not completed_trades:
                return None
            
            trade_returns = [t['收益率'] for t in completed_trades]
            hold_days = [t['持有天数'] for t in completed_trades]
            
            win_trades = len([r for r in trade_returns if r > 0])
            win_rate = win_trades / len(completed_trades)
            avg_return = np.mean(trade_returns)
            avg_win = np.mean([r for r in trade_returns if r > 0]) if win_trades > 0 else 0
            avg_loss = np.mean([r for r in trade_returns if r < 0]) if (len(completed_trades) - win_trades) > 0 else 0
            avg_hold_days = np.mean(hold_days)
            
            # 策略整体表现
            strategy_returns = data[f'{strategy_name}_收益率']
            total_return = data[f'{strategy_name}_累计收益'].iloc[-1] - 1
            
            # 年化收益率
            years = (data['日期'].iloc[-1] - data['日期'].iloc[0]).days / 365.25
            annual_return = (1 + total_return) ** (1/years) - 1
            
            # 波动率
            volatility = strategy_returns.std() * np.sqrt(252)
            
            # 夏普比率
            risk_free_rate = 0.03
            sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
            
            # 最大回撤
            cumulative = data[f'{strategy_name}_累计收益']
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # 持股时间比例
            holding_ratio = data[f'{strategy_name}_持仓'].mean()
            
            return {
                '策略名称': strategy_name,
                '总交易次数': len(completed_trades),
                '获胜交易': win_trades,
                '胜率': win_rate * 100,
                '平均收益率': avg_return,
                '平均获胜收益': avg_win,
                '平均亏损': avg_loss,
                '平均持有天数': avg_hold_days,
                '持股时间比例': holding_ratio * 100,
                '总收益率': total_return * 100,
                '年化收益率': annual_return * 100,
                '年化波动率': volatility * 100,
                '夏普比率': sharpe_ratio,
                '最大回撤': max_drawdown * 100
            }
            
        except Exception as e:
            logger.error(f"计算绩效时出错: {e}")
            return None
    
    def plot_strategy_results(self, data, all_performance):
        """绘制策略结果图表"""
        try:
            logger.info("绘制策略结果图表...")
            
            # 计算买入持有基准
            if '基准_累计收益' not in data.columns:
                data['基准_收益率'] = data['收盘价'].pct_change().fillna(0)
                data['基准_累计收益'] = (1 + data['基准_收益率']).cumprod()
            
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('基于美债的ERP择时策略回测结果', fontsize=16, fontweight='bold')
            
            # 1. 累计收益对比
            ax1 = axes[0, 0]
            ax1.plot(data['日期'], (data['基准_累计收益'] - 1) * 100, 
                    'k-', linewidth=2, label='买入持有', alpha=0.8)
            
            colors = ['red', 'blue', 'green', 'orange']
            strategy_names = ['保守', '积极', '均衡', '最近5年']
            
            for i, strategy_name in enumerate(strategy_names):
                if f'{strategy_name}_累计收益' in data.columns:
                    ax1.plot(data['日期'], (data[f'{strategy_name}_累计收益'] - 1) * 100,
                            color=colors[i], linewidth=1.5, label=f'{strategy_name}策略')
            
            ax1.set_title('累计收益率对比')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('累计收益率 (%)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. ERP走势与信号
            ax2 = axes[0, 1]
            ax2.plot(data['日期'], data['ERP_百分比'], 'b-', linewidth=1, label='ERP', alpha=0.8)
            
            # 添加分位数线
            ax2.axhline(y=data['ERP_百分比'].quantile(0.85), color='red', linestyle='--', alpha=0.7, label='85%分位')
            ax2.axhline(y=data['ERP_百分比'].quantile(0.75), color='orange', linestyle='--', alpha=0.7, label='75%分位')
            ax2.axhline(y=data['ERP_百分比'].quantile(0.5), color='gray', linestyle='--', alpha=0.7, label='50%分位')
            
            ax2.set_title('ERP走势与择时阈值')
            ax2.set_xlabel('日期')
            ax2.set_ylabel('ERP (%)')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. 策略绩效对比
            ax3 = axes[1, 0]
            if all_performance:
                strategies = list(all_performance.keys())
                annual_returns = [all_performance[s]['年化收益率'] for s in strategies]
                max_drawdowns = [abs(all_performance[s]['最大回撤']) for s in strategies]
                
                x = np.arange(len(strategies))
                width = 0.35
                
                bars1 = ax3.bar(x - width/2, annual_returns, width, label='年化收益率', alpha=0.8)
                bars2 = ax3.bar(x + width/2, max_drawdowns, width, label='最大回撤', alpha=0.8, color='red')
                
                ax3.set_title('策略绩效对比')
                ax3.set_xlabel('策略')
                ax3.set_ylabel('百分比 (%)')
                ax3.set_xticks(x)
                ax3.set_xticklabels(strategies, rotation=45)
                ax3.legend()
                ax3.grid(True, alpha=0.3)
                
                # 添加数值标签
                for bar in bars1:
                    height = bar.get_height()
                    ax3.text(bar.get_x() + bar.get_width()/2., height,
                            f'{height:.1f}%', ha='center', va='bottom')
            
            # 4. 夏普比率对比
            ax4 = axes[1, 1]
            if all_performance:
                sharpe_ratios = [all_performance[s]['夏普比率'] for s in strategies]
                win_rates = [all_performance[s]['胜率'] for s in strategies]
                
                ax4_twin = ax4.twinx()
                
                bars1 = ax4.bar(x - width/2, sharpe_ratios, width, label='夏普比率', alpha=0.8, color='blue')
                bars2 = ax4_twin.bar(x + width/2, win_rates, width, label='胜率', alpha=0.8, color='green')
                
                ax4.set_title('夏普比率与胜率')
                ax4.set_xlabel('策略')
                ax4.set_ylabel('夏普比率', color='blue')
                ax4_twin.set_ylabel('胜率 (%)', color='green')
                ax4.set_xticks(x)
                ax4.set_xticklabels(strategies, rotation=45)
                ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            image_path = os.path.join(self.image_dir, 'erp_us_based_timing_strategy_results.png')
            plt.savefig(image_path, dpi=300, bbox_inches='tight')
            logger.info(f"策略结果图表已保存到: {image_path}")
            
            plt.close()
            
        except Exception as e:
            logger.error(f"绘制策略结果图表时出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("基于美债的ERP择时策略回测")
    print("=" * 60)
    print("新公式: ERP = 1/PE - 十年期美债收益率")
    
    # 创建策略分析器
    analyzer = ERPUSBasedTimingStrategy()
    
    # 显示策略参数
    print(f"\n📋 策略参数:")
    for name, params in analyzer.strategies.items():
        print(f"  {name}: {params['description']}")
    
    # 加载数据
    data = analyzer.load_erp_data()
    if data is None:
        print("\n❌ 数据加载失败")
        return
    
    print(f"\n✅ 数据加载成功！")
    print(f"回测期间: {data['日期'].min().strftime('%Y-%m-%d')} 到 {data['日期'].max().strftime('%Y-%m-%d')}")
    print(f"数据点数: {len(data)} 个")
    
    # 计算择时信号
    data_with_signals = analyzer.calculate_signals(data)
    if data_with_signals is None:
        print("\n❌ 信号计算失败")
        return
    
    # 回测各策略
    all_results = {}
    all_performance = {}
    
    strategy_names = ['保守', '积极', '均衡', '最近5年']
    
    for strategy_name in strategy_names:
        print(f"\n📊 回测策略: {strategy_name}")
        
        trades = analyzer.backtest_strategy(data_with_signals, strategy_name)
        performance = analyzer.calculate_performance(data_with_signals, strategy_name, trades)
        
        if performance:
            all_results[strategy_name] = trades
            all_performance[strategy_name] = performance
            
            print(f"  总交易次数: {performance['总交易次数']}")
            print(f"  胜率: {performance['胜率']:.1f}%")
            print(f"  年化收益率: {performance['年化收益率']:.2f}%")
            print(f"  最大回撤: {performance['最大回撤']:.2f}%")
            print(f"  夏普比率: {performance['夏普比率']:.2f}")
            print(f"  持股时间比例: {performance['持股时间比例']:.1f}%")
    
    # 计算买入持有基准
    if data_with_signals is not None:
        benchmark_total_return = (data_with_signals['收盘价'].iloc[-1] / data_with_signals['收盘价'].iloc[0] - 1) * 100
        years = (data_with_signals['日期'].iloc[-1] - data_with_signals['日期'].iloc[0]).days / 365.25
        benchmark_annual_return = ((1 + benchmark_total_return/100) ** (1/years) - 1) * 100
        
        # 计算基准最大回撤
        prices = data_with_signals['收盘价']
        cumulative = prices / prices.iloc[0]
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        benchmark_max_drawdown = drawdown.min() * 100
    
    # 显示绩效对比
    if all_performance:
        print("\n" + "=" * 80)
        print("策略绩效对比")
        print("=" * 80)
        
        print(f"买入持有基准:")
        print(f"  年化收益率: {benchmark_annual_return:.2f}%")
        print(f"  最大回撤: {benchmark_max_drawdown:.2f}%")
        
        print(f"\n策略表现排名（按年化收益率）:")
        sorted_strategies = sorted(all_performance.items(), 
                                 key=lambda x: x[1]['年化收益率'], reverse=True)
        
        for i, (name, perf) in enumerate(sorted_strategies, 1):
            excess_return = perf['年化收益率'] - benchmark_annual_return
            print(f"\n{i}. {name}策略:")
            print(f"   年化收益率: {perf['年化收益率']:7.2f}%")
            print(f"   超额收益: {excess_return:+7.2f}%")
            print(f"   最大回撤: {perf['最大回撤']:7.2f}%")
            print(f"   夏普比率: {perf['夏普比率']:7.2f}")
            print(f"   胜率: {perf['胜率']:7.1f}%")
            print(f"   持股比例: {perf['持股时间比例']:7.1f}%")
    
    # 绘制策略结果
    analyzer.plot_strategy_results(data_with_signals, all_performance)
    
    # 当前市场建议
    latest = data_with_signals.iloc[-1]
    current_erp_percentile = latest['ERP_分位数']
    current_erp_5y_percentile = latest.get('ERP_5年分位数', None)
    
    print(f"\n🎯 当前市场状态与策略建议:")
    print("-" * 50)
    print(f"当前ERP: {latest['ERP_百分比']:.2f}%")
    print(f"ERP历史分位数: {current_erp_percentile:.1f}%")
    if current_erp_5y_percentile is not None:
        print(f"ERP最近5年分位数: {current_erp_5y_percentile:.1f}%")
    
    if current_erp_percentile >= 85:
        print("💡 建议: ERP处于极高分位，强烈建议买入")
    elif current_erp_percentile >= 75:
        print("💡 建议: ERP处于较高分位，建议买入")
    elif current_erp_percentile >= 70:
        print("💡 建议: ERP处于中高分位，可考虑买入")
    elif current_erp_percentile <= 30:
        print("💡 建议: ERP处于低分位，建议持现金或减仓")
    else:
        print("💡 建议: ERP处于中等分位，保持当前配置")
    
    print(f"\n✅ 基于美债的ERP择时策略回测完成！")

if __name__ == "__main__":
    main()
