#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取恒生指数数据和PE值
"""

import akshare as ak
import pandas as pd
import numpy as np

def get_hsi_spot_data():
    """获取恒生指数实时数据"""
    print("=" * 60)
    print("获取恒生指数实时数据")
    print("=" * 60)
    
    try:
        # 获取香港指数实时数据
        hk_data = ak.stock_hk_index_spot_em()
        
        if hk_data is not None and not hk_data.empty:
            print(f"获取到香港指数数据，总数: {len(hk_data)}")
            
            # 筛选恒生指数相关数据
            hsi_keywords = ['恒生指数', 'HSI', '恒指', 'Hang Seng']
            hsi_data = hk_data[hk_data['名称'].str.contains('|'.join(hsi_keywords), na=False)]
            
            print(f"\n找到 {len(hsi_data)} 个恒生相关指数:")
            print("-" * 80)
            for _, row in hsi_data.iterrows():
                print(f"代码: {row['代码']:>10} | 名称: {row['名称']:>20} | 最新价: {row['最新价']:>10.2f} | 涨跌幅: {row['涨跌幅']:>6.2f}%")
            
            return hsi_data
        else:
            print("未获取到数据")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return pd.DataFrame()

def get_hsi_historical_data():
    """获取恒生指数历史数据"""
    print("\n" + "=" * 60)
    print("获取恒生指数历史数据")
    print("=" * 60)
    
    try:
        # 尝试获取恒生指数历史数据
        hsi_hist = ak.stock_hk_index_daily_em(symbol="HSI")
        
        if hsi_hist is not None and not hsi_hist.empty:
            print(f"获取到恒生指数历史数据，总数: {len(hsi_hist)}")
            print(f"列名: {list(hsi_hist.columns)}")
            print(f"日期范围: {hsi_hist['日期'].min()} 到 {hsi_hist['日期'].max()}")
            
            print("\n最新5天数据:")
            print(hsi_hist.tail())
            
            return hsi_hist
        else:
            print("未获取到历史数据")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"获取历史数据时出错: {e}")
        return pd.DataFrame()

def get_hsi_valuation():
    """尝试获取恒生指数估值数据"""
    print("\n" + "=" * 60)
    print("获取恒生指数估值数据")
    print("=" * 60)
    
    # 尝试一些可能的恒生指数代码
    hsi_codes = ["HSI", "00001", "^HSI", "HSTECH", "HSCEI"]
    
    for code in hsi_codes:
        try:
            print(f"\n尝试代码: {code}")
            valuation_data = ak.stock_hk_valuation_baidu(symbol=code)
            
            if valuation_data is not None and not valuation_data.empty:
                print(f"✅ 成功获取估值数据，总数: {len(valuation_data)}")
                print(f"列名: {list(valuation_data.columns)}")
                print(f"日期范围: {valuation_data['date'].min()} 到 {valuation_data['date'].max()}")
                
                print("\n最新5天数据:")
                print(valuation_data.tail())
                
                return valuation_data, code
            else:
                print("❌ 未获取到数据")
                
        except Exception as e:
            print(f"❌ 代码 {code} 出错: {e}")
    
    return pd.DataFrame(), None

def calculate_pe_from_price_earnings():
    """尝试通过价格和盈利数据计算PE"""
    print("\n" + "=" * 60)
    print("尝试计算恒生指数PE")
    print("=" * 60)
    
    try:
        # 获取恒生指数历史数据
        hsi_hist = ak.stock_hk_index_daily_em(symbol="HSI")
        
        if hsi_hist is not None and not hsi_hist.empty:
            print(f"获取到恒生指数数据: {len(hsi_hist)} 条记录")
            
            # 获取最新价格
            latest_price = hsi_hist['收盘'].iloc[-1]
            latest_date = hsi_hist['日期'].iloc[-1]
            
            print(f"最新收盘价: {latest_price:.2f} ({latest_date})")
            
            # 这里需要恒生指数的盈利数据来计算PE
            # 由于akshare可能没有直接的恒生指数PE数据，我们可以：
            # 1. 使用历史PE估算
            # 2. 查找其他数据源
            # 3. 使用成分股加权计算
            
            print("\n注意: 需要恒生指数的盈利数据来计算准确的PE值")
            print("建议使用专业金融数据提供商的数据")
            
            return hsi_hist
        else:
            print("未获取到恒生指数数据")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"计算PE时出错: {e}")
        return pd.DataFrame()

def get_hsi_pe_from_web():
    """尝试从网络获取恒生指数PE数据"""
    print("\n" + "=" * 60)
    print("尝试从其他源获取恒生指数PE")
    print("=" * 60)
    
    try:
        # 尝试使用akshare的其他接口
        # 这里可以尝试一些可能包含PE数据的接口
        
        # 方法1: 尝试获取港股通数据
        try:
            hk_connect = ak.stock_hk_ggt_components_em()
            print(f"获取到港股通成分股数据: {len(hk_connect)} 条")
            print(f"列名: {list(hk_connect.columns)}")
            
            # 查看是否有PE相关数据
            pe_columns = [col for col in hk_connect.columns if 'PE' in col or '市盈率' in col or 'pe' in col.lower()]
            if pe_columns:
                print(f"找到PE相关列: {pe_columns}")
            
        except Exception as e:
            print(f"获取港股通数据出错: {e}")
        
        # 方法2: 尝试获取指数基本信息
        try:
            index_info = ak.index_stock_info()
            hsi_info = index_info[index_info['display_name'].str.contains('恒生', na=False)]
            if not hsi_info.empty:
                print(f"找到恒生相关指数信息:")
                print(hsi_info)
        except Exception as e:
            print(f"获取指数信息出错: {e}")
            
        print("\n建议:")
        print("1. 使用专业金融数据API (如Wind, Bloomberg)")
        print("2. 从金融网站爬取数据")
        print("3. 使用恒生指数成分股数据计算加权PE")
        
    except Exception as e:
        print(f"获取PE数据时出错: {e}")

def main():
    """主函数"""
    print("恒生指数数据获取工具")
    
    # 获取实时数据
    spot_data = get_hsi_spot_data()
    
    # 获取历史数据
    hist_data = get_hsi_historical_data()
    
    # 尝试获取估值数据
    valuation_data, code = get_hsi_valuation()
    
    # 尝试计算PE
    price_data = calculate_pe_from_price_earnings()
    
    # 尝试从其他源获取PE
    get_hsi_pe_from_web()
    
    # 总结
    print("\n" + "=" * 60)
    print("数据获取总结")
    print("=" * 60)
    
    if not spot_data.empty:
        main_hsi = spot_data[spot_data['代码'] == 'HSI']
        if not main_hsi.empty:
            row = main_hsi.iloc[0]
            print(f"恒生指数 (HSI): {row['最新价']:.2f} ({row['涨跌幅']:+.2f}%)")
    
    if not hist_data.empty:
        print(f"历史数据: {len(hist_data)} 条记录")
        print(f"最新收盘价: {hist_data['收盘'].iloc[-1]:.2f}")
    
    if not valuation_data.empty:
        print(f"估值数据: {len(valuation_data)} 条记录 (代码: {code})")
    
    print("\n注意: akshare可能没有直接的恒生指数PE数据")
    print("建议使用其他专业数据源获取PE值")

if __name__ == "__main__":
    main()
